<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test d'injection multiple - Bullets Extension</title>
</head>
<body>
    <h1>Page de test pour l'extension Bullets</h1>
    <p>Cette page sert à tester si l'extension gère correctement les injections multiples du script content.js.</p>
    
    <h2>Instructions de test :</h2>
    <ol>
        <li>Chargez cette page dans Chrome avec l'extension Bullets installée</li>
        <li>Faites un clic droit et sélectionnez "Bullet page" plusieurs fois</li>
        <li>Vérifiez qu'il n'y a pas d'erreur "Identifier 'popupElement' has already been declared" dans la console</li>
        <li>Vérifiez que la popup s'affiche correctement à chaque fois</li>
    </ol>
    
    <h2>Contenu de test</h2>
    <p>Voici du contenu de test pour que l'extension ait quelque chose à résumer :</p>
    
    <article>
        <h3>L'intelligence artificielle transforme notre quotidien</h3>
        <p>L'intelligence artificielle (IA) est devenue omniprésente dans notre vie quotidienne. Des assistants vocaux aux recommandations de contenu, en passant par les voitures autonomes, l'IA façonne notre façon de vivre, de travailler et d'interagir.</p>
        
        <p>Les avancées récentes dans le domaine de l'apprentissage automatique ont permis de développer des systèmes capables de comprendre le langage naturel, de reconnaître des images avec une précision remarquable, et même de créer du contenu original.</p>
        
        <p>Cependant, ces progrès soulèvent également des questions importantes concernant l'éthique, la vie privée et l'impact sur l'emploi. Il est crucial de développer l'IA de manière responsable, en tenant compte de ces enjeux sociétaux.</p>
        
        <p>L'avenir de l'IA promet des innovations encore plus spectaculaires, avec des applications dans la médecine, l'éducation, l'environnement et bien d'autres domaines. La clé sera de trouver le bon équilibre entre innovation technologique et bien-être humain.</p>
    </article>
    
    <script>
        // Script pour tester les injections multiples
        console.log('Page de test chargée');
        
        // Simuler des clics multiples pour tester
        let clickCount = 0;
        document.addEventListener('contextmenu', function(e) {
            clickCount++;
            console.log(`Clic droit #${clickCount} détecté`);
        });
    </script>
</body>
</html>
